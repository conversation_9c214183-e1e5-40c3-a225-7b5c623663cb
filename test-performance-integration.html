<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Integration Test</title>
    <link href="dist/output.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
        }
        .test-fail {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        .test-info {
            background-color: #f0f9ff;
            border: 1px solid #bae6fd;
            color: #0369a1;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="test-container">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Performance Integration Test</h1>
        
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Module Loading Tests</h2>
            <div id="module-tests"></div>
            <button id="test-modules" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Test Module Loading
            </button>
        </div>
        
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Performance Integration Tests</h2>
            <div id="integration-tests"></div>
            <button id="test-integration" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Test Performance Integration
            </button>
        </div>
        
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Search Performance Tests</h2>
            <div id="search-tests"></div>
            <button id="test-search" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                Test Search Performance
            </button>
        </div>
        
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Performance Metrics</h2>
            <div id="metrics-display"></div>
            <button id="show-metrics" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                Show Performance Metrics
            </button>
        </div>
    </div>

    <script type="module">
        // Test module loading
        document.getElementById('test-modules').addEventListener('click', async () => {
            const container = document.getElementById('module-tests');
            container.innerHTML = '';
            
            const modules = [
                { name: 'Logger', path: './modules/logger.js' },
                { name: 'Performance Monitor', path: './modules/performance-monitor.js' },
                { name: 'Search Optimizer', path: './modules/search-optimizer.js' },
                { name: 'Search Cache', path: './modules/search-cache.js' },
                { name: 'Async DOM Updater', path: './modules/async-dom-updater.js' },
                { name: 'Optimized Virtual Scroll', path: './modules/optimized-virtual-scroll.js' },
                { name: 'Progressive Search', path: './modules/progressive-search.js' },
                { name: 'Performance Integration', path: './modules/performance-integration.js' }
            ];
            
            for (const module of modules) {
                try {
                    const startTime = performance.now();
                    await import(module.path);
                    const loadTime = performance.now() - startTime;
                    
                    container.innerHTML += `
                        <div class="test-result test-pass">
                            ✅ ${module.name} loaded successfully (${loadTime.toFixed(2)}ms)
                        </div>
                    `;
                } catch (error) {
                    container.innerHTML += `
                        <div class="test-result test-fail">
                            ❌ ${module.name} failed to load: ${error.message}
                        </div>
                    `;
                }
            }
        });
        
        // Test performance integration
        document.getElementById('test-integration').addEventListener('click', async () => {
            const container = document.getElementById('integration-tests');
            container.innerHTML = '';
            
            try {
                // Import performance integration
                const { performanceIntegration } = await import('./modules/performance-integration.js');
                
                container.innerHTML += `
                    <div class="test-result test-pass">
                        ✅ Performance integration imported successfully
                    </div>
                `;
                
                // Create test container
                const testContainer = document.createElement('div');
                testContainer.id = 'test-app';
                testContainer.innerHTML = '<div id="conversation-list"></div>';
                document.body.appendChild(testContainer);
                
                // Initialize
                await performanceIntegration.initialize(testContainer, {
                    debugMode: true,
                    enablePerformanceMonitoring: true
                });
                
                container.innerHTML += `
                    <div class="test-result test-pass">
                        ✅ Performance integration initialized successfully
                    </div>
                `;
                
                // Test search
                const searchResult = await performanceIntegration.performOptimizedSearch('test search', {});
                
                container.innerHTML += `
                    <div class="test-result test-pass">
                        ✅ Search test completed (ID: ${searchResult})
                    </div>
                `;
                
                // Cleanup
                document.body.removeChild(testContainer);
                
            } catch (error) {
                container.innerHTML += `
                    <div class="test-result test-fail">
                        ❌ Performance integration test failed: ${error.message}
                    </div>
                `;
            }
        });
        
        // Test search performance
        document.getElementById('test-search').addEventListener('click', async () => {
            const container = document.getElementById('search-tests');
            container.innerHTML = '';
            
            try {
                const { PerformanceTestSuite } = await import('./test/performance-test-suite.js');
                
                container.innerHTML += `
                    <div class="test-result test-info">
                        🔄 Running performance test suite...
                    </div>
                `;
                
                const testSuite = new PerformanceTestSuite();
                const results = await testSuite.runAllTests();
                
                container.innerHTML += `
                    <div class="test-result test-pass">
                        ✅ Performance test suite completed
                    </div>
                    <div class="test-result test-info">
                        📊 Total tests: ${results.totalTests}<br>
                        ✅ Passed: ${results.summary.passed}<br>
                        ❌ Failed: ${results.summary.failed}<br>
                        ⚠️ Warnings: ${results.summary.warnings}
                    </div>
                `;
                
                // Show detailed results
                for (const [category, data] of Object.entries(results.categories)) {
                    const status = data.analysis.status;
                    const statusClass = status === 'passed' ? 'test-pass' : 
                                       status === 'failed' ? 'test-fail' : 'test-info';
                    
                    container.innerHTML += `
                        <div class="test-result ${statusClass}">
                            ${status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⚠️'} 
                            ${category}: ${data.results.length} tests
                            ${data.analysis.averageImprovement ? 
                              ` (${data.analysis.averageImprovement.toFixed(2)}x improvement)` : ''}
                        </div>
                    `;
                }
                
            } catch (error) {
                container.innerHTML += `
                    <div class="test-result test-fail">
                        ❌ Performance test failed: ${error.message}
                    </div>
                `;
            }
        });
        
        // Show performance metrics
        document.getElementById('show-metrics').addEventListener('click', async () => {
            const container = document.getElementById('metrics-display');
            container.innerHTML = '';
            
            try {
                if (window.getPerformanceMetrics) {
                    const metrics = window.getPerformanceMetrics();
                    
                    if (metrics) {
                        container.innerHTML = `
                            <div class="test-result test-info">
                                <h3 class="font-semibold mb-2">Performance Metrics:</h3>
                                <pre class="text-sm overflow-auto">${JSON.stringify(metrics, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        container.innerHTML = `
                            <div class="test-result test-info">
                                ℹ️ Performance metrics not available (integration not initialized)
                            </div>
                        `;
                    }
                } else {
                    container.innerHTML = `
                        <div class="test-result test-info">
                            ℹ️ Performance metrics function not available
                        </div>
                    `;
                }
            } catch (error) {
                container.innerHTML += `
                    <div class="test-result test-fail">
                        ❌ Failed to get performance metrics: ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
